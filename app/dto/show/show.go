package show

import (
	"strings"

	"vlab/app/common/dbs"
	"vlab/app/dto/common"
)

type ClassListReq struct {
	Limit uint32 `json:"limit" form:"limit"`
}

type ClassListResp struct {
	Total uint64           `json:"total"`
	List  []*ClassListItem `json:"list"`
	Sorts *ClassListItem   `json:"sorts"`
}

type ClassListItem struct {
	ClassID uint64            `json:"class_id"`
	Name    string            `json:"name"`
	Total   uint64            `json:"total"`
	List    []*ClassFieldItem `json:"list"`
}

type ClassFieldItem struct {
	ID      uint64 `json:"id"`
	ClassId uint64 `json:"class_id"`
	Name    string `json:"name"`
	Cover   string `json:"cover,omitempty"`
	Desc    string `json:"desc,omitempty"`
	SortReq
}

type ShowFilterReq struct {
	ChannelID uint64 `form:"channel_id" json:"channel_id,omitempty" binding:"omitempty"` // 渠道ID

	VersionID uint64 `form:"version_id" json:"version_id,omitempty" binding:"omitempty"` // 版本ID
}

type ShowRecommendListReq struct {
	common.DataListReq

	UserID uint64 `form:"user_id" json:"user_id,omitempty" binding:"omitempty"` // 用户ID

	ISO_639_1 string `form:"iso_639_1" json:"iso_639_1,omitempty" binding:"omitempty"` // 语言标识

	ShowFilterReq
}

type ShowRecommendListResp struct {
	List  []*ShowRecommendListItem `json:"list"` // 剧列表
	Count int64                    `json:"count"`
}

type ShowRecommendListItem struct {
	*ShowBase

	Recommend *RecommendBase `json:"recommend"`
}

type RecommendBase struct {
	Name  string     `json:"name"`  // 推荐名称
	Image *ImageBase `json:"image"` // 图片
}

type ShowAssignListReq struct {
	common.DataListReq

	ISO_639_1 string `form:"iso_639_1" json:"iso_639_1,omitempty" binding:"omitempty"` // 语言标识

	ShowFilterReq
}

type ShowAssignListResp struct {
	*ShowListResp
}

type ShowPopularListReq struct {
	common.DataListReq

	ISO_639_1 string `form:"iso_639_1" json:"iso_639_1,omitempty" binding:"omitempty"` // 语言标识

	ShowFilterReq
}

type ShowPopularListResp struct {
	*ShowListResp
}

type ShowSearchReq struct {
	common.DataListReq

	Keyword string `form:"keyword" json:"keyword" binding:"omitempty"` // encode过后的关键字

	GenreID     uint64 `form:"genre_id" json:"genre_id,omitempty" binding:"omitempty"`         // 类型ID
	FranchiseID uint64 `form:"franchise_id" json:"franchise_id,omitempty" binding:"omitempty"` // 系列ID
	ShowID      uint64 `form:"show_id" json:"show_id,omitempty" binding:"omitempty"`           // 剧集ID
	PersonID    uint64 `form:"person_id" json:"person_id,omitempty" binding:"omitempty"`       // 人物ID

	UserID uint64 `form:"user_id" json:"user_id,omitempty" binding:"omitempty"` // 用户ID

	ISO_639_1 string `form:"iso_639_1" json:"iso_639_1,omitempty" binding:"omitempty"` // 语言标识

	FieldIDs []uint64 `form:"field_ids" json:"field_ids,omitempty" binding:"omitempty"` // 领域ID

	// 向量搜索相关参数
	UseVector    bool    `form:"use_vector" json:"use_vector,omitempty" binding:"omitempty"`       // 是否使用向量搜索
	DenseWeight  float64 `form:"dense_weight" json:"dense_weight,omitempty" binding:"omitempty"`   // 稠密向量权重
	SparseWeight float64 `form:"sparse_weight" json:"sparse_weight,omitempty" binding:"omitempty"` // 稀疏向量权重
	VectorTopK   int     `form:"vector_top_k" json:"vector_top_k,omitempty" binding:"omitempty"`   // 向量搜索候选数量

	SortReq

	ShowFilterReq
}

type SortReq struct {
	Type       uint32         `form:"type" json:"type,omitempty" binding:"omitempty"` // 搜索类型 1-最新 2-最热 3-评分
	SortMethod dbs.SortMethod `form:"sort_method" json:"sort_method,omitempty" binding:"omitempty,oneof=asc desc"`
}

func (slr SortReq) CheckOrder() bool {
	order := strings.ToLower(string(slr.SortMethod))
	if order == string(dbs.SortMethodAsc) {
		return false
	}
	return true
}

type ShowSearchResp struct {
	*ShowListResp
}

// ShowListReq 首页剧集列表请求
type ShowListReq struct {
	common.DataListReq

	ISO_639_1 string   `form:"iso_639_1" json:"iso_639_1,omitempty" binding:"omitempty"` // 语言标识
	UserID    uint64   `form:"user_id" json:"user_id,omitempty" binding:"omitempty"`     // 用户ID
	FieldIDs  []uint64 `form:"field_ids" json:"field_ids,omitempty" binding:"omitempty"` // 领域ID

	ContentType uint32 `form:"content_type" json:"content_type,omitempty" binding:"omitempty"` // 内容类型

	SortReq
	ShowFilterReq
}

// ShowListResp 首页剧集列表响应
type ShowListResp struct {
	List  []*ShowBase `json:"list"` // 剧列表
	Count int64       `json:"count"`
}

// ShowDetailReq 剧集详情请求
type ShowDetailReq struct {
	ShowID uint64 `form:"show_id" json:"show_id" binding:"required"`

	UserID uint64 `form:"user_id" json:"user_id,omitempty" binding:"omitempty"` // 用户ID

	ISO_639_1 string `form:"iso_639_1" json:"iso_639_1,omitempty" binding:"omitempty"` // 语言标识

	ShowFilterReq
}

// ShowDetailResp 剧集详情响应
type ShowDetailResp struct {
	*ShowBase

	Homepage    string        `json:"homepage"`   // 官网
	CreatedBy   []*CreditBase `json:"created_by"` // 导演
	Credits     *Credits      `json:"credits"`
	Description string        `json:"description"` // 描述

	Episodes []*EpisodeBase `json:"episodes"`

	//Seasons []*SeasonBase `json:"seasons"` // 季
	Images       *Images     `json:"images"`        // 图片
	RelatedShows []*ShowBase `json:"related_shows"` // 关联剧列表（来自同一关联池的其他剧）
}

type Credits struct {
	Casts []*CreditBase `json:"casts"` // 演员
	Crews []*CreditBase `json:"crews"` // 工作人员
}

type Images struct {
	Posters   []*ImageBase `json:"posters"`    // 海报
	BackDrops []*ImageBase `json:"back_drops"` // 背景
}
