package lok

import (
	"errors"
	"fmt"
	"strconv"
	"strings"

	"vlab/app/api/aliyun/common"
	"vlab/app/api/auto"
	"vlab/app/common/config"
	"vlab/app/common/dbs"
	"vlab/app/dao"
	classFieldDao "vlab/app/dao/content_class_field"
	classMappingDao "vlab/app/dao/content_class_mapping"
	episode "vlab/app/dao/content_episode"
	i18nDao "vlab/app/dao/content_i18n"
	showDao "vlab/app/dao/content_show"
	videoDao "vlab/app/dao/content_video"
	showDto "vlab/app/dto/show"
	"vlab/app/service/show"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
	"vlab/pkg/log"

	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

//show *showDao.Show,
//episodes []*episodeDao.Episode,
//videos []*videoDao.Video,
//subtitles []*subtitleDao.Subtitle,
//images []*imageDao.Image,
//genres []*genreDao.Genre,
//classes []*genreDao.Genre,
//franchise *franchiseDao.Franchise,
//person []*personDao.Person,

func switchClarity(s string) uint32 {
	switch s {
	case "GROOT_HD":
		return uint32(common.Resolution_1920)
	case "GROOT_SD":
		return uint32(common.Resolution_1280)
	case "GROOT_LD":
		return uint32(common.Resolution_848)
	case "GROOT_FD":
		return uint32(common.Resolution_640)
	}
	return 0
}

// 'fr': 'fr_FR',
//
//	'ru': 'ru_RU',
//	'pt': 'pt_PT',
//	'ar': 'ar_AR',
//	'es': 'es_ES',
//	'en': 'en_US',
//	'zh_CN': 'zh_CN',
//	'in_ID': 'id_ID'
func switchIso639(s string) string {
	switch s {
	case "en":
		return string(config.EnUs)
	case "in_ID":
		return string(config.IdId)
	case "zh_CN":
		return string(config.ZhCn)
	case "zh_TW":
		return string(config.ZhTw)
	case "th":
		return string(config.ThTh)
	case "ms":
		return string(config.MsMy)
	case "ja":
		return string(config.JaJp)
	case "ar":
		return string(config.ArAr)
	case "es":
		return string(config.EsEs)
	case "pt":
		return string(config.PtPt)
	case "fr":
		return string(config.FrFr)
	case "ru":
		return string(config.RuRu)
	}
	return ""
}

func switchDetailType(s string) uint32 {
	switch strings.ToLower(s) {
	case "movie":
		return uint32(showDao.ContentTypeMovie)
	case "tv":
		return uint32(showDao.ContentTypeTV)
	case "comic":
		return uint32(showDao.ContentTypeComic)
	}
	return 0
}

type classFieldWithMapping struct {
	*classFieldDao.Model
	MappingID uint64
}

type genre struct {
	Origin  *auto.IosStappOpenApiIDNameVO
	Mapping *classMappingDao.Model
}

func checkGenre(ctx *gin.Context, iso6391 string, classID uint64, mappingType classMappingDao.ClassMappingType, genres []*auto.IosStappOpenApiIDNameVO) map[uint64]uint64 {
	var (
		existClassIDs    = make([]uint64, 0)
		existMappingList = make(classMappingDao.ModelList, 0)
		err              error

		mappingIDs      = make([]uint64, 0)
		mappingMap      = make(map[uint64]*auto.IosStappOpenApiIDNameVO)
		existMappingMap = make(map[uint64]genre)

		newMappingList    = make([]*classMappingDao.Model, 0)
		newClassFieldList = make([]*classFieldWithMapping, 0)
		newList           = make([]*classFieldDao.Model, 0)

		ret = make(map[uint64]uint64) // key为mappingID, value为classID
	)

	for _, item := range genres {
		d := item.Id
		mappingIDs = append(mappingIDs, d)
		if _, ok := mappingMap[d]; ok {
			continue
		}
		mappingMap[d] = item
	}

	existMappingList, err = classMappingDao.GetRepo().FindByFilter(ctx, &classMappingDao.Filter{
		MappingType: mappingType,
		MappingIDs:  mappingIDs,
	})
	if err != nil {
		return ret
	}

	for _, e := range existMappingList {
		existClassIDs = append(existClassIDs, e.ClassID)
		ret[e.MappingID] = e.ClassID
		if item, ok := mappingMap[e.MappingID]; ok {
			existMappingMap[e.ClassID] = genre{
				Origin:  item,
				Mapping: e,
			}
			delete(mappingMap, e.MappingID) // 剔除已存在的
		}
	}

	if len(existClassIDs) > 0 {
		existClassIDs = lo.Uniq(existClassIDs)
		i18List := make(i18nDao.I18nList, 0)
		existClassList, err := classFieldDao.GetRepo().FindByFilter(ctx, &classFieldDao.Filter{
			IDs: existClassIDs,
		})
		if err != nil {
			return nil
		}
		for _, e := range existClassList {
			item := &i18nDao.I18n{
				Key:       e.NameKey,
				ISO_639_1: iso6391,
			}
			if p, ok := existMappingMap[e.ID]; ok {
				item.Value = p.Origin.Name
			}

			i18List = append(i18List, item)
		}

		err = i18nDao.GetRepo().BatchCreateOrUpdate(ctx, i18List)
		if err != nil {
			log.WithContext(ctx).WithFields(logrus.Fields{
				"mappingType": mappingType,
				"mappingIDs":  mappingIDs,
			}).WithError(err).Error("i18n create failed")
			return ret
		}
	}

	for _, item := range genres {
		if _, ok := mappingMap[item.Id]; !ok {
			continue
		} // 已存在则跳过

		ncf := &classFieldDao.Model{
			ClassID: classID,
			Name:    item.Name,
			NameKey: getKey(ctx, item.Name, iso6391),
		}
		newList = append(newList, ncf)
		tmp := &classFieldWithMapping{
			Model:     ncf,
			MappingID: item.Id,
		}
		newClassFieldList = append(newClassFieldList, tmp)
	}

	// batch create class field
	err = classFieldDao.GetRepo().BatchCreate(ctx, newList)
	if err != nil {
		log.WithContext(ctx).WithFields(logrus.Fields{
			"mappingType": mappingType,
			"mappingIDs":  mappingIDs,
		}).WithError(err).Error("class field create failed")
		return ret
	}

	for _, model := range newClassFieldList {
		newMappingList = append(newMappingList, &classMappingDao.Model{
			ClassID:     model.ID,
			MappingType: mappingType,
			MappingID:   model.MappingID,
		})
		ret[model.MappingID] = model.ID
	}

	// batch create class mapping
	err = classMappingDao.GetRepo().BatchCreate(ctx, newMappingList)
	if err != nil {
		log.WithContext(ctx).WithFields(logrus.Fields{
			"mappingType": mappingType,
			"mappingIDs":  mappingIDs,
		}).WithError(err).Error("class mapping create failed")
		return ret
	}

	return ret
}

type playZone struct {
	Origin  *auto.PlayZoneNameVO
	Mapping *classMappingDao.Model
}

func getKey(ctx *gin.Context, s string, iso6391 string) string {
	if s == "" {
		return ""
	}
	var (
		showSrv = show.GetService()

		req = &showDto.GenI18nKeyReq{
			List: make([]*dao.I18n, 0),
		}

		i18n = &dao.I18n{
			ISO_639_1: iso6391,
			Value:     s,
		}
	)

	req.List = append(req.List, i18n)

	resp, err := showSrv.AdminI18nCreate(ctx, &showDto.AdminI18nCreateReq{
		List: []*dao.I18n{i18n},
	})
	if err != nil {
		log.WithContext(ctx).WithFields(logrus.Fields{
			"value": s,
		}).WithError(err).Error("i18n create failed")
		return ""
	}

	return resp.Key
}

func checkPlayZone(ctx *gin.Context, iso6391 string, classID uint64, mappingType classMappingDao.ClassMappingType, playZoneList []*auto.PlayZoneNameVO) map[uint64]uint64 {
	var (
		existClassIDs    = make([]uint64, 0)
		existMappingList = make(classMappingDao.ModelList, 0)
		err              error

		mappingIDs      = make([]uint64, 0)
		mappingMap      = make(map[uint64]*auto.PlayZoneNameVO)
		existMappingMap = make(map[uint64]playZone)

		newMappingList    = make([]*classMappingDao.Model, 0)
		newClassFieldList = make([]*classFieldWithMapping, 0)
		newList           = make([]*classFieldDao.Model, 0)

		ret = make(map[uint64]uint64) // key为mappingID, value为classID
	)

	for _, item := range playZoneList {
		d := item.Id
		mappingIDs = append(mappingIDs, d)
		if _, ok := mappingMap[d]; ok {
			continue
		}
		mappingMap[d] = item
	}

	existMappingList, err = classMappingDao.GetRepo().FindByFilter(ctx, &classMappingDao.Filter{
		MappingType: mappingType,
		MappingIDs:  mappingIDs,
	})
	if err != nil {
		return ret
	}

	for _, e := range existMappingList {
		existClassIDs = append(existClassIDs, e.ClassID)
		ret[e.MappingID] = e.ClassID
		if item, ok := mappingMap[e.MappingID]; ok {
			existMappingMap[e.ClassID] = playZone{
				Origin:  item,
				Mapping: e,
			}
			delete(mappingMap, e.MappingID) // 剔除已存在的
		}
	}

	if len(existClassIDs) > 0 {
		existClassIDs = lo.Uniq(existClassIDs)
		i18List := make(i18nDao.I18nList, 0)
		existClassList, err := classFieldDao.GetRepo().FindByFilter(ctx, &classFieldDao.Filter{
			IDs: existClassIDs,
		})
		if err != nil {
			return nil
		}
		for _, e := range existClassList {
			item := &i18nDao.I18n{
				Key:       e.NameKey,
				ISO_639_1: iso6391,
			}
			if p, ok := existMappingMap[e.ID]; ok {
				item.Value = p.Origin.Name
			}

			i18List = append(i18List, item)
		}

		err = i18nDao.GetRepo().BatchCreateOrUpdate(ctx, i18List)
		if err != nil {
			log.WithContext(ctx).WithFields(logrus.Fields{
				"mappingType": mappingType,
				"mappingIDs":  mappingIDs,
			}).WithError(err).Error("i18n create failed")
			return ret
		}
	}

	for _, item := range playZoneList {
		if _, ok := mappingMap[item.Id]; !ok {
			continue
		} // 已存在则跳过

		ncf := &classFieldDao.Model{
			ClassID: classID,
			Name:    item.Name,
			NameKey: getKey(ctx, item.Name, iso6391),
		}
		newList = append(newList, ncf)
		tmp := &classFieldWithMapping{
			Model:     ncf,
			MappingID: item.Id,
		}
		newClassFieldList = append(newClassFieldList, tmp)
	}

	// batch create class field
	err = classFieldDao.GetRepo().BatchCreate(ctx, newList)
	if err != nil {
		log.WithContext(ctx).WithFields(logrus.Fields{
			"mappingType": mappingType,
			"mappingIDs":  mappingIDs,
		}).WithError(err).Error("class field create failed")
		return ret
	}

	for _, model := range newClassFieldList {
		newMappingList = append(newMappingList, &classMappingDao.Model{
			ClassID:     model.ID,
			MappingType: mappingType,
			MappingID:   model.MappingID,
		})
		ret[model.MappingID] = model.ID
	}

	// batch create class mapping
	err = classMappingDao.GetRepo().BatchCreate(ctx, newMappingList)
	if err != nil {
		log.WithContext(ctx).WithFields(logrus.Fields{
			"mappingType": mappingType,
			"mappingIDs":  mappingIDs,
		}).WithError(err).Error("class mapping create failed")
		return ret
	}

	return ret
}

func ToShowVWXYZ(c *gin.Context, detail *auto.DetailResp) (showID uint64, err error) {
	var (
		doUpdate bool

		ctx = helper.GenGinCtx()

		lokShowID, _ = strconv.ParseUint(detail.MotionPictureId, 10, 64)

		showSrv = show.GetService()

		getKeyFn = func(s string) string {
			if s == "" {
				return ""
			}
			var (
				req = &showDto.GenI18nKeyReq{
					List: make([]*dao.I18n, 0),
				}

				i18n = &dao.I18n{
					ISO_639_1: switchIso639(detail.Iso6391),
					Value:     s,
				}
			)

			req.List = append(req.List, i18n)

			resp, err := showSrv.AdminI18nCreate(ctx, &showDto.AdminI18nCreateReq{
				List: []*dao.I18n{i18n},
			})
			if err != nil {
				log.WithContext(ctx).WithFields(logrus.Fields{
					"lokShowID": lokShowID,
					"value":     s,
				}).WithError(err).Error("i18n create failed")
				return ""
			}

			return resp.Key
		}

		eg errgroup.Group

		playZoneClassList         = make([]*showDto.Class, 0)
		presentationTimeClassList = make([]*showDto.Class, 0)
		genreClassList            = make([]*showDto.Class, 0)

		iso6391 = switchIso639(detail.Iso6391)
	)
	if iso6391 == "" {
		log.WithContext(c).WithFields(logrus.Fields{
			"lokShowID": detail.MotionPictureId,
			"iso6391":   detail.Iso6391,
		}).Error("iso6391 not support")
		return 0, errors.New("iso6391 not support")
	}

	if lokShowID <= 0 {
		return 0, nil
	}

	{
		// 校验该映射是否存在
		showIDs, err := showDao.GetRepo().FindMappingXidsByFilter(ctx, &showDao.MappingFilter{
			MappingID:   lokShowID,
			MappingType: 1,
		}, dbs.PluckShowID)
		if err != nil {
			log.WithContext(ctx).WithFields(logrus.Fields{
				"lokShowID": lokShowID,
			}).WithError(err).Error("mapping find failed")
			return 0, err
		}
		if len(showIDs) > 0 {
			log.WithContext(ctx).WithField(
				"lokShowID", lokShowID,
			).Info("mapping exist")
			doUpdate = true
			showID = showIDs[0]
		}
	}

	// 地区关系
	eg.Go(func() error {

		var (
			classID      = uint64(2) // 地区
			playZoneList = detail.PlayZoneNameVOS

			mappingKeyClassMap = make(map[uint64]uint64)
		)

		mappingKeyClassMap = checkPlayZone(ctx, switchIso639(detail.Iso6391), classID, classMappingDao.ClassMappingZone, playZoneList)

		for _, u := range mappingKeyClassMap {
			tmp := &showDto.Class{
				ClassID: classID,
				FieldID: u,
			}
			playZoneClassList = append(playZoneClassList, tmp)
		}

		return nil
	})

	// 年份关系
	eg.Go(func() error {
		var (
			classID = uint64(3) // 年份
			pTime   = detail.PresentationTime
			ret     = &showDto.ClassFieldListResp{}
			fieldID = uint64(0)
		)

		ret, err = showSrv.ClassFieldList(ctx, &showDto.ClassFieldListReq{
			ClassID:   classID,
			FieldName: fmt.Sprintf("%d", pTime),
		})
		if err != nil {
			return err
		}
		if len(ret.List) == 0 {
			create, err := showSrv.AdminClassFieldCreate(ctx, &showDto.AdminClassFieldCreateReq{
				ClassID: classID,
				Name:    fmt.Sprintf("%d", pTime),
			})
			if err != nil {
				return err
			}
			fieldID = create.ID
		} else {
			fieldID = ret.List[0].ID
		}

		presentationTimeClassList = append(presentationTimeClassList, &showDto.Class{
			ClassID: classID,
			Name:    fmt.Sprintf("%d", pTime),
			FieldID: fieldID,
		})

		return nil
	})

	// genre关系
	eg.Go(func() error {
		var (
			classID   = uint64(5)
			genreList = detail.IosStappOpenApiIDNameVOS

			mappingKeyClassMap = make(map[uint64]uint64)
		)

		mappingKeyClassMap = checkGenre(ctx, switchIso639(detail.Iso6391), classID, classMappingDao.ClassMappingZone, genreList)

		for _, u := range mappingKeyClassMap {
			tmp := &showDto.Class{
				ClassID: classID,
				FieldID: u,
			}
			genreClassList = append(genreClassList, tmp)
		}

		return nil
	})

	if err = eg.Wait(); err != nil {
		log.WithContext(ctx).WithFields(logrus.Fields{
			"lokShowID": lokShowID,
		}).WithError(err).Error("i18n create failed")
		return 0, err
	}

	var (
		req = &showDto.AdminShowCreateReq{
			Name:        detail.CinematicName,
			OverView:    detail.BriefIntroduction,
			ContentType: switchDetailType(detail.TvShowType.ProgrammeType),
			AirDate:     "", AirDateKey: "",
			Genres:    make([]uint64, 0),         // deprecated
			Classes:   make([]*showDto.Class, 0), // TODO: 预先创建
			Franchise: 0, Homepage: "", InProduction: 0,
			Langs: []string{
				iso6391,
			},
			Images:           make([]*showDto.ImageBase, 0),
			Credits:          make([]*showDto.CreditBase, 0), // TODO: 预先创建
			PresentationTime: uint32(detail.PresentationTime),
		}
		tx = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	)

	req.Classes = append(req.Classes, playZoneClassList...)
	req.Classes = append(req.Classes, presentationTimeClassList...)
	req.Classes = append(req.Classes, genreClassList...)

	req.Score = uint32(detail.CreditScore * 10)

	req.Images = append(req.Images, &showDto.ImageBase{
		Iso6391:    iso6391,
		FilePath:   detail.StandingCoverLink,
		AspectType: 2,
	})

	req.Images = append(req.Images, &showDto.ImageBase{
		Iso6391:    iso6391,
		FilePath:   detail.HorizontalImageLink,
		AspectType: 1,
	})

	if doUpdate { // TODO: UPDATE or Merge UPDATE?
		var (
			showModel = &showDao.Show{}
			err       error

			i18nList = make(i18nDao.I18nList, 0)
		)
		showModel, err = showDao.GetRepo().FetchByID(ctx, showID)
		if err != nil {
			return 0, err
		}
		if showModel == nil || showModel.ID <= 0 {
			log.WithContext(ctx).WithFields(logrus.Fields{
				"lokShowID": lokShowID,
				"showID":    showID,
			}).Warn("show not exist")
			return 0, ecode.NotFoundErr
		}
		// 若为更新, 则直接选用原model上的key
		req.NameKey = showModel.NameKey
		i18nList = append(i18nList, &i18nDao.I18n{
			Key:       showModel.NameKey,
			ISO_639_1: iso6391,
			Value:     detail.CinematicName,
		})
		req.OverViewKey = showModel.OverviewKey
		i18nList = append(i18nList, &i18nDao.I18n{
			Key:       showModel.OverviewKey,
			ISO_639_1: iso6391,
			Value:     detail.BriefIntroduction,
		})
		err = i18nDao.GetRepo().BatchCreateOrUpdateWithTx(ctx, tx, i18nList)
		if err != nil {
			log.WithContext(ctx).WithFields(logrus.Fields{
				"lokShowID": lokShowID,
				"showID":    showID,
			}).WithError(err).Error("i18n create failed")
			return 0, err
		}

		_, err = showSrv.ImportShowUpdate(ctx, &showDto.AdminShowUpdateReq{
			ID:                 showID,
			AdminShowCreateReq: req,
			Iso_639_1:          iso6391,
		}, tx)
		if err != nil {
			log.WithContext(ctx).WithFields(logrus.Fields{
				"lokShowID": lokShowID,
				"showID":    showID,
			}).WithError(err).Error("show update failed")
			tx.Rollback()
			return 0, err
		}
	} else {
		req.NameKey = getKeyFn(detail.CinematicName)
		req.OverViewKey = getKeyFn(detail.BriefIntroduction)

		createResp, err := showSrv.AdminShowCreate(ctx, req, tx)
		if err != nil {
			log.WithContext(ctx).WithFields(logrus.Fields{
				"lokShowID": lokShowID,
			}).WithError(err).Error("show create failed")
			tx.Rollback()
			return 0, err
		}
		showID = createResp.ID
	}

	{
		_, err = showDao.GetRepo().MappingCreateOrUpdateWithTx(
			ctx, tx, &showDao.ShowWithMapping{
				ShowID:      showID,
				MappingType: 1,
				MappingID:   lokShowID,
			})
		if err != nil {
			log.WithContext(ctx).WithFields(logrus.Fields{
				"showID":    showID,
				"lokShowID": lokShowID,
			}).WithError(err).Error("mapping create failed")
			tx.Rollback()
			return 0, err
		}
	}

	if len(detail.IosStappContentEpisodeVos) > 0 {
		var (
			episodeBatchReq = &showDto.AdminEpisodeCreateBatchReq{
				ShowID: showID,
				List:   make([]*showDto.AdminEpisodeCreateBatchItem, 0),
			}
			lokEpisodeIDs    = make([]uint64, 0)
			lokEpisodeMap    = make(map[uint64]struct{})
			existEpisodeList = make(episode.EpisodeList, 0)
			existVideoList   = make(videoDao.VideoList, 0)

			newEpisodeVideoResp = &showDto.AdminEpisodeCreateBatchResp{}
		)

		for _, vo := range detail.IosStappContentEpisodeVos {
			lokEpisodeIDs = append(lokEpisodeIDs, vo.RightFilmId)
			lokEpisodeMap[vo.RightFilmId] = struct{}{}
		}

		existEpisodeList, err = episode.GetRepo().FindByFilter(ctx, &episode.Filter{
			MappingType: 1,
			MappingIDs:  lokEpisodeIDs,
		})
		if err != nil {
			tx.Rollback()
			log.WithContext(ctx).WithFields(logrus.Fields{
				"lokShowID": lokShowID,
				"showID":    showID,
			}).WithError(err).Error("episode find failed")
			return 0, err
		}
		for _, e := range existEpisodeList {
			if _, ok := lokEpisodeMap[e.MappingID]; ok {
				delete(lokEpisodeMap, e.MappingID) // 剔除已存在的
			}
		}

		if ids := existEpisodeList.GetIDs(); len(ids) > 0 {
			existVideoList, err = videoDao.GetRepo().FindByFilter(ctx, &videoDao.Filter{
				EpisodeIDs: ids,
			})
			if err != nil {
				log.WithContext(ctx).WithFields(logrus.Fields{
					"lokShowID": lokShowID,
					"showID":    showID,
				}).WithError(err).Error("video find failed")
				tx.Rollback()
				return 0, err
			}
		}

		for _, vo := range detail.IosStappContentEpisodeVos {
			if _, ok := lokEpisodeMap[vo.RightFilmId]; !ok { // 已存在则跳过
				continue
			}
			//episodeNameKey := getKeyFn(vo.RightFilmName)

			item := &showDto.AdminEpisodeCreateBatchItem{
				Name: vo.RightFilmName,
				//NameKey:       episodeNameKey,
				EpisodeNumber: uint32(vo.FullSeason),
				Video: &showDto.AdminEpisodeVideo{
					PathType: uint32(videoDao.PathTypeUrl),
					UrlPaths: make([]*showDto.UrlPathItem, 0),
				},
				MappingType: 1,
				MappingID:   vo.RightFilmId,
			}

			for _, info := range vo.IosStappDefinitionInfos {
				if info.PlayResp == nil {
					log.WithContext(ctx).WithFields(logrus.Fields{
						"lokEpisodeID": vo.RightFilmId,
						"clarityCode":  info.ClarityCode,
					}).Warn("playResp is nil")
					continue
				}
				vo := &showDto.UrlPathItem{
					Resolution: switchClarity(info.ClarityCode),
					Url:        info.CinemaUrl,
				}
				item.Video.UrlPaths = append(item.Video.UrlPaths, vo)
			}

			episodeBatchReq.List = append(episodeBatchReq.List, item)
		}

		newEpisodeVideoResp, err = showSrv.AdminEpisodeImportBatch(
			ctx,
			episodeBatchReq,
			tx,
		)
		if err != nil {
			log.WithContext(ctx).WithFields(logrus.Fields{
				"lokShowID": lokShowID,
				"showID":    showID,
			}).WithError(err).Error("episode create batch failed")
			tx.Rollback()
			return 0, err
		}
		/*
			1. 接口返回不带集序号,入库依靠顺序写入序号. 若接口顺序发生变化, 则无法感知
			2. 若存在集映射, 目前不处理, 若当前集都是转码中视频, 则无法补充视频, 同理, 无法补充视频
		*/

		// 处理字幕时, 直接取当前lokEpisode映射的所有集和视频的ID, 有则更新, 无则创建
		existEpisodeList = append(existEpisodeList, newEpisodeVideoResp.EpisodeList...)
		existEpisodeList = lo.UniqBy(existEpisodeList, func(item *episode.Episode) uint64 {
			return item.ID
		})

		existVideoList = append(existVideoList, newEpisodeVideoResp.VideoList...)
		existVideoList = lo.UniqBy(existVideoList, func(item *videoDao.Video) uint64 {
			return item.ID
		})

		videoMap := existVideoList.GetEpisodeIDMap()

		type VideoMappingKey struct {
			MappingType uint32
			MappingID   uint64
		}

		type VideoMapping struct {
			Episode *episode.Episode
			Video   *videoDao.Video
		}

		episodeMapping := existEpisodeList.GetMappingKeyMap()
		newMapping := make(map[VideoMappingKey]*VideoMapping)

		for k, v := range episodeMapping {
			if _, ok := videoMap[v.ID]; !ok {
				continue
			}
			for _, video := range videoMap[v.ID] {
				kk := VideoMappingKey{
					MappingType: k.MappingType,
					MappingID:   k.MappingID,
				}
				newMapping[kk] = &VideoMapping{
					Episode: v,
					Video:   video,
				}
			}
		}

		subTitleList := make([]*showDto.AdminSubtitleCreateBatchReq, 0)

		for _, vo := range detail.IosStappContentEpisodeVos {
			kk := VideoMappingKey{
				MappingType: 1,
				MappingID:   vo.RightFilmId,
			}
			if _, ok := newMapping[kk]; !ok {
				continue
			}
			video := newMapping[kk].Video
			epi := newMapping[kk].Episode

			item := &showDto.AdminSubtitleCreateBatchReq{
				VideoID:   video.ID,
				EpisodeID: epi.ID,
				List:      make([]*showDto.AdminSubtitleCreateBatchItem, 0),
			}
			for _, info := range vo.IosStappubtitlingInfos {
				var iso_639_1 string
				iso_639_1 = switchIso639(info.WordAbbr)
				if iso_639_1 == "" {
					continue
				}

				item.List = append(item.List, &showDto.AdminSubtitleCreateBatchItem{
					ISO_639_1: iso_639_1,
					FilePath:  info.WordUrl,
				})

			}
			subTitleList = append(subTitleList, item)
		}

		err = showSrv.AdminSubtitleBatchImport(
			ctx,
			subTitleList,
			tx,
		)
		if err != nil {
			log.WithContext(ctx).WithFields(logrus.Fields{
				"lokShowID": lokShowID,
				"showID":    showID,
			}).WithError(err).Error("subtitle batch import failed")
			tx.Rollback()
			return 0, err
		}
	}

	tx.Commit()

	return showID, nil
}

func ToEpisode(ctx *gin.Context, detail *auto.PlayReq) (err error) {
	if detail == nil || detail.FullVersionID <= 0 {
		log.WithContext(ctx).WithFields(logrus.Fields{
			"detail": detail,
		}).Warn("detail is nil or FullVersionID is 0")
		return ecode.ParamErr
	}

	var (
		lokEpisodeID = detail.FullVersionID
		episodes     = make([]*episode.Episode, 0)
		episodeModel *episode.Episode
		videos       = make([]*videoDao.Video, 0)
		videoModel   *videoDao.Video

		showSrv = show.GetService()

		req = &showDto.AdminVideoImportBatchReq{}
	)

	episodes, err = episode.GetRepo().FindByFilter(ctx, &episode.Filter{
		MappingType: 1,
		MappingID:   lokEpisodeID,
	})
	if err != nil {
		return err
	}
	if len(episodes) == 0 {
		log.WithContext(ctx).WithFields(logrus.Fields{
			"lokEpisodeID": lokEpisodeID,
		}).Warn("episode not exist")
		err = ecode.NotFoundErr
		return
	}
	if len(episodes) > 1 {
		log.WithContext(ctx).WithFields(logrus.Fields{
			"lokEpisodeID": lokEpisodeID,
		}).Warn("episode more than one")
	}
	episodeModel = episodes[0]

	videos, err = videoDao.GetRepo().FindByFilter(ctx, &videoDao.Filter{
		EpisodeID: episodeModel.ID,
	})
	if err != nil {
		return err
	}

	log.WithContext(ctx).WithFields(logrus.Fields{
		"lokEpisodeID": lokEpisodeID,
		"episodeID":    episodeModel.ID,
	}).Info("find episode videos")

	if len(videos) == 0 {
		log.WithContext(ctx).WithFields(logrus.Fields{
			"lokEpisodeID": lokEpisodeID,
		}).Warn("video not exist")
		err = ecode.NotFoundErr
		return
	}
	if len(videos) > 1 {
		log.WithContext(ctx).WithFields(logrus.Fields{
			"lokEpisodeID": lokEpisodeID,
		}).Warn("video more than one")
	}
	videoModel = videos[0]

	req = &showDto.AdminVideoImportBatchReq{
		EpisodeID: episodeModel.ID,
		VideoID:   videoModel.ID,
		Name:      videoModel.Name,
		Video: &showDto.AdminEpisodeVideo{
			VideoID:   videoModel.ID,
			PathType:  uint32(videoDao.PathTypeUrl),
			VideoPath: videoModel.VideoPath,
			UrlPaths:  make([]*showDto.UrlPathItem, 0),
			Runtime:   detail.PlayResp.AllTime,
		},
	}

	req.Video.UrlPaths = append(req.Video.UrlPaths, &showDto.UrlPathItem{
		Resolution: switchClarity(detail.VisualEffects),
		Url:        detail.CinemaUrl,
	})

	_, err = showSrv.AdminEpisodeVideoImportBatch(ctx, req)
	if err != nil {
		return err
	}
	return nil
}
