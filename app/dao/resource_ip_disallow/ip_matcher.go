package resource_ip_disallow

import (
	"net"
	"strings"
	"sync"

	"github.com/samber/lo"
)

// IPTrieNode Trie树节点，用于优化IP匹配
type IPTrieNode struct {
	children map[byte]*IPTrieNode
	isEnd    bool
	cidr     string // 存储原始CIDR字符串，用于调试
}

// IPMatcher IP匹配器，使用Trie树优化CIDR匹配性能
type IPMatcher struct {
	root  *IPTrieNode
	cidrs []string // 原始CIDR列表，用于fallback
	mutex sync.RWMutex
}

// NewIPMatcher 创建新的IP匹配器
func NewIPMatcher() *IPMatcher {
	return &IPMatcher{
		root: &IPTrieNode{
			children: make(map[byte]*IPTrieNode),
		},
		cidrs: make([]string, 0),
	}
}

// BuildFromIPList 从IP列表构建Trie树
func (m *IPMatcher) BuildFromIPList(ipList string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 重置
	m.root = &IPTrieNode{
		children: make(map[byte]*IPTrieNode),
	}
	m.cidrs = make([]string, 0)

	if ipList == "" {
		return nil
	}

	// 解析IP列表
	ips := strings.Split(ipList, ",")
	cleanIPs := lo.Map(ips, func(ip string, _ int) string {
		return strings.TrimSpace(ip)
	})

	// 构建Trie树
	for _, cidr := range cleanIPs {
		if cidr == "" {
			continue
		}

		m.cidrs = append(m.cidrs, cidr)

		// 如果不包含/，则认为是单个IP地址，添加/32或/128
		if !strings.Contains(cidr, "/") {
			if strings.Contains(cidr, ":") {
				cidr = cidr + "/128" // IPv6
			} else {
				cidr = cidr + "/32" // IPv4
			}
		}

		// 解析CIDR
		_, ipNet, err := net.ParseCIDR(cidr)
		if err != nil {
			// 如果解析失败，尝试作为单个IP处理
			if ip := net.ParseIP(strings.TrimSuffix(cidr, "/32")); ip != nil {
				m.insertIP(ip, cidr)
			}
			continue
		}

		// 将网络地址插入Trie树
		m.insertNetwork(ipNet, cidr)
	}

	return nil
}

// insertIP 插入单个IP到Trie树
func (m *IPMatcher) insertIP(ip net.IP, originalCIDR string) {
	current := m.root

	// 统一转换为16字节格式（IPv4会被映射到IPv6）
	ip = ip.To16()
	if ip == nil {
		return
	}

	for _, b := range ip {
		if current.children[b] == nil {
			current.children[b] = &IPTrieNode{
				children: make(map[byte]*IPTrieNode),
			}
		}
		current = current.children[b]
	}

	current.isEnd = true
	current.cidr = originalCIDR
}

// insertNetwork 插入网络段到Trie树（简化版本，不在Trie树中处理CIDR）
func (m *IPMatcher) insertNetwork(ipNet *net.IPNet, originalCIDR string) {
	// 对于CIDR网络段，我们不在Trie树中处理
	// 而是依赖fallbackMatch方法进行准确的CIDR匹配
	// 这里只是为了保持接口一致性
	return
}

// IsIPMatched 检查IP是否匹配（使用优化的传统方法）
func (m *IPMatcher) IsIPMatched(clientIP string) bool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	// 解析客户端IP
	ip := net.ParseIP(clientIP)
	if ip == nil {
		return false
	}

	// 首先尝试Trie树精确匹配（仅用于单个IP）
	if m.trieMatch(ip) {
		return true
	}

	// 使用传统方法进行CIDR匹配
	return m.fallbackMatch(clientIP)
}

// trieMatch 使用Trie树进行匹配（简化版本，主要用于精确IP匹配）
func (m *IPMatcher) trieMatch(ip net.IP) bool {
	ip = ip.To16()
	if ip == nil {
		return false
	}

	current := m.root

	// 遍历IP的每个字节，寻找精确匹配
	for _, b := range ip {
		if next, exists := current.children[b]; exists {
			current = next
		} else {
			// 没有找到精确匹配的路径
			return false
		}
	}

	// 检查最终节点是否标记为结束
	return current.isEnd
}

// fallbackMatch 传统匹配方式（作为备用）
func (m *IPMatcher) fallbackMatch(clientIP string) bool {
	clientIPAddr := net.ParseIP(clientIP)
	if clientIPAddr == nil {
		return false
	}

	// 检查每个CIDR
	for _, cidr := range m.cidrs {
		if cidr == "" {
			continue
		}

		// 如果不包含/，则认为是单个IP地址
		if !strings.Contains(cidr, "/") {
			if cidr == clientIP {
				return true
			}
			continue
		}

		// 解析CIDR
		_, ipNet, err := net.ParseCIDR(cidr)
		if err != nil {
			// 如果解析失败，尝试作为单个IP处理
			if cidr == clientIP {
				return true
			}
			continue
		}

		// 检查IP是否在CIDR范围内
		if ipNet.Contains(clientIPAddr) {
			return true
		}
	}

	return false
}

// GetStats 获取匹配器统计信息
func (m *IPMatcher) GetStats() map[string]interface{} {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	return map[string]interface{}{
		"cidr_count": len(m.cidrs),
		"trie_nodes": m.countTrieNodes(m.root),
	}
}

// countTrieNodes 递归计算Trie树节点数量
func (m *IPMatcher) countTrieNodes(node *IPTrieNode) int {
	if node == nil {
		return 0
	}

	count := 1
	for _, child := range node.children {
		count += m.countTrieNodes(child)
	}
	return count
}
