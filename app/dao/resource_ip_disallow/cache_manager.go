package resource_ip_disallow

import (
	"context"
	"fmt"
	"net/http"
	"sync"
	"time"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// CacheManager 缓存管理器，负责缓存预热、监控和优化
type CacheManager struct {
	repo  Repo
	stats *CacheStats
	mutex sync.RWMutex
}

// CacheStats 缓存统计信息
type CacheStats struct {
	HitCount       int64     `json:"hit_count"`
	MissCount      int64     `json:"miss_count"`
	ErrorCount     int64     `json:"error_count"`
	LastUpdateTime time.Time `json:"last_update_time"`
	WarmupChannels []uint64  `json:"warmup_channels"`
	TotalChannels  int       `json:"total_channels"`
	CacheSize      int       `json:"cache_size"`
}

var (
	cacheManager     *CacheManager
	cacheManagerOnce sync.Once
)

// GetCacheManager 获取缓存管理器单例
func GetCacheManager() *CacheManager {
	cacheManagerOnce.Do(func() {
		cacheManager = &CacheManager{
			repo: GetRepo(),
			stats: &CacheStats{
				LastUpdateTime: time.Now(),
				WarmupChannels: make([]uint64, 0),
			},
		}
	})
	return cacheManager
}

// WarmupCache 缓存预热
func (cm *CacheManager) WarmupCache(ctx context.Context, channelIDs []uint64) error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	log.WithContext(ctx).WithField("channels", channelIDs).Info("Starting cache warmup")

	successCount := 0
	errorCount := 0

	for _, channelID := range channelIDs {
		ginCtx := &gin.Context{}
		ginCtx.Request = &http.Request{}
		ginCtx.Request = ginCtx.Request.WithContext(ctx)

		// 预热分层缓存
		_, err := cm.repo.RedisIPDisallowByChannelID(ginCtx, channelID)
		if err != nil {
			log.WithContext(ctx).WithError(err).WithField("channel_id", channelID).
				Error("Failed to warmup cache for channel")
			errorCount++
			continue
		}

		successCount++
	}

	// 更新统计信息
	cm.stats.WarmupChannels = channelIDs
	cm.stats.LastUpdateTime = time.Now()
	cm.stats.TotalChannels = len(channelIDs)

	log.WithContext(ctx).WithFields(logrus.Fields{
		"success_count": successCount,
		"error_count":   errorCount,
		"total_count":   len(channelIDs),
	}).Info("Cache warmup completed")

	return nil
}

// WarmupHotChannels 预热热点渠道缓存
func (cm *CacheManager) WarmupHotChannels(ctx context.Context, limit int) error {
	ginCtx := &gin.Context{}
	ginCtx.Request = &http.Request{}
	ginCtx.Request = ginCtx.Request.WithContext(ctx)

	// 获取启用的渠道配置
	filter := &Filter{
		Status: 1, // 只获取启用的配置
	}

	list, err := cm.repo.FindByFilter(ginCtx, filter)
	if err != nil {
		return fmt.Errorf("failed to fetch active channels: %w", err)
	}

	// 提取渠道ID
	channelIDs := make([]uint64, 0, len(list))
	for _, item := range list {
		channelIDs = append(channelIDs, item.ChannelID)
		if len(channelIDs) >= limit {
			break
		}
	}

	return cm.WarmupCache(ctx, channelIDs)
}

// RecordCacheHit 记录缓存命中
func (cm *CacheManager) RecordCacheHit() {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	cm.stats.HitCount++
}

// RecordCacheMiss 记录缓存未命中
func (cm *CacheManager) RecordCacheMiss() {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	cm.stats.MissCount++
}

// RecordCacheError 记录缓存错误
func (cm *CacheManager) RecordCacheError() {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	cm.stats.ErrorCount++
}

// GetCacheStats 获取缓存统计信息
func (cm *CacheManager) GetCacheStats() *CacheStats {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	// 创建副本避免并发问题
	statsCopy := *cm.stats
	return &statsCopy
}

// GetCacheHitRate 获取缓存命中率
func (cm *CacheManager) GetCacheHitRate() float64 {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	total := cm.stats.HitCount + cm.stats.MissCount
	if total == 0 {
		return 0.0
	}

	return float64(cm.stats.HitCount) / float64(total)
}

// ClearChannelCache 清除指定渠道的缓存
func (cm *CacheManager) ClearChannelCache(ctx context.Context, channelID uint64) error {
	ginCtx := &gin.Context{}
	ginCtx.Request = &http.Request{}
	ginCtx.Request = ginCtx.Request.WithContext(ctx)

	// 清除分层缓存
	if err := cm.repo.RedisClearIPDisallowByChannelID(ginCtx, channelID); err != nil {
		cm.RecordCacheError()
		return fmt.Errorf("failed to clear channel cache: %w", err)
	}

	log.WithContext(ctx).WithField("channel_id", channelID).Info("Channel cache cleared")
	return nil
}

// ClearAllCache 清除所有缓存
func (cm *CacheManager) ClearAllCache(ctx context.Context) error {
	ginCtx := &gin.Context{}
	ginCtx.Request = &http.Request{}
	ginCtx.Request = ginCtx.Request.WithContext(ctx)

	// 清除全局缓存
	if err := cm.repo.RedisClearIPDisallowList(ginCtx); err != nil {
		cm.RecordCacheError()
		return fmt.Errorf("failed to clear global cache: %w", err)
	}

	log.WithContext(ctx).Info("All IP disallow cache cleared")
	return nil
}

// StartCacheMonitoring 启动缓存监控
func (cm *CacheManager) StartCacheMonitoring(ctx context.Context, interval time.Duration) {
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			log.WithContext(ctx).Info("Cache monitoring stopped")
			return
		case <-ticker.C:
			cm.logCacheStats(ctx)
		}
	}
}

// logCacheStats 记录缓存统计信息
func (cm *CacheManager) logCacheStats(ctx context.Context) {
	stats := cm.GetCacheStats()
	hitRate := cm.GetCacheHitRate()

	log.WithContext(ctx).WithFields(logrus.Fields{
		"hit_count":      stats.HitCount,
		"miss_count":     stats.MissCount,
		"error_count":    stats.ErrorCount,
		"hit_rate":       fmt.Sprintf("%.2f%%", hitRate*100),
		"total_channels": stats.TotalChannels,
		"last_update":    stats.LastUpdateTime.Format("2006-01-02 15:04:05"),
	}).Info("IP blacklist cache statistics")
}

// OptimizeCache 缓存优化建议
func (cm *CacheManager) OptimizeCache(ctx context.Context) map[string]interface{} {
	stats := cm.GetCacheStats()
	hitRate := cm.GetCacheHitRate()

	recommendations := make([]string, 0)

	// 分析命中率
	if hitRate < 0.8 {
		recommendations = append(recommendations, "Cache hit rate is low, consider warming up more channels")
	}

	// 分析错误率
	total := stats.HitCount + stats.MissCount + stats.ErrorCount
	if total > 0 {
		errorRate := float64(stats.ErrorCount) / float64(total)
		if errorRate > 0.05 {
			recommendations = append(recommendations, "High error rate detected, check Redis connectivity")
		}
	}

	// 分析缓存大小
	if stats.TotalChannels > 1000 {
		recommendations = append(recommendations, "Large number of channels, consider implementing LRU eviction")
	}

	return map[string]interface{}{
		"stats":           stats,
		"hit_rate":        hitRate,
		"recommendations": recommendations,
		"optimization_tips": []string{
			"Use layered caching for better performance",
			"Implement cache preloading for hot channels",
			"Monitor cache hit rates regularly",
			"Consider using local cache for frequently accessed data",
		},
	}
}
