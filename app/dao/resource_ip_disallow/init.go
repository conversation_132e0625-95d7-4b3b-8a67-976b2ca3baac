package resource_ip_disallow

import (
	"context"
	"time"
	"vlab/pkg/log"
)

// InitCacheSystem 初始化缓存系统
func InitCacheSystem(ctx context.Context) error {
	cacheManager := GetCacheManager()
	
	// 预热热点渠道缓存（前50个活跃渠道）
	if err := cacheManager.WarmupHotChannels(ctx, 50); err != nil {
		log.WithContext(ctx).WithError(err).Error("Failed to warmup hot channels cache")
		// 不返回错误，允许系统继续启动
	}
	
	// 启动缓存监控（每5分钟记录一次统计信息）
	go cacheManager.StartCacheMonitoring(ctx, 5*time.Minute)
	
	log.WithContext(ctx).Info("IP blacklist cache system initialized successfully")
	return nil
}

// GetCacheOptimizationReport 获取缓存优化报告
func GetCacheOptimizationReport(ctx context.Context) map[string]interface{} {
	cacheManager := GetCacheManager()
	return cacheManager.OptimizeCache(ctx)
}

// WarmupSpecificChannels 预热指定渠道的缓存
func WarmupSpecificChannels(ctx context.Context, channelIDs []uint64) error {
	cacheManager := GetCacheManager()
	return cacheManager.WarmupCache(ctx, channelIDs)
}

// ClearChannelCache 清除指定渠道的缓存
func ClearChannelCache(ctx context.Context, channelID uint64) error {
	cacheManager := GetCacheManager()
	return cacheManager.ClearChannelCache(ctx, channelID)
}

// GetCacheStatistics 获取缓存统计信息
func GetCacheStatistics() map[string]interface{} {
	cacheManager := GetCacheManager()
	stats := cacheManager.GetCacheStats()
	hitRate := cacheManager.GetCacheHitRate()
	
	return map[string]interface{}{
		"hit_count":       stats.HitCount,
		"miss_count":      stats.MissCount,
		"error_count":     stats.ErrorCount,
		"hit_rate":        hitRate,
		"hit_rate_percent": hitRate * 100,
		"last_update":     stats.LastUpdateTime,
		"warmup_channels": stats.WarmupChannels,
		"total_channels":  stats.TotalChannels,
	}
}
