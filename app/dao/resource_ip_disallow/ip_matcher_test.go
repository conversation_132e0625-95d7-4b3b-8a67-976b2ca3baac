package resource_ip_disallow

import (
	"testing"
	"time"
)

func TestIPMatcher_BasicMatching(t *testing.T) {
	matcher := NewIPMatcher()
	
	// 测试IP列表
	ipList := "***********, 10.0.0.0/8, **********/12, 2001:db8::/32"
	
	err := matcher.BuildFromIPList(ipList)
	if err != nil {
		t.Fatalf("Failed to build IP matcher: %v", err)
	}
	
	// 测试用例
	testCases := []struct {
		ip       string
		expected bool
		desc     string
	}{
		{"***********", true, "exact IPv4 match"},
		{"***********", false, "IPv4 no match"},
		{"********", true, "IPv4 CIDR match"},
		{"************", true, "IPv4 CIDR match"},
		{"************", false, "IPv4 outside CIDR"},
		{"*******", false, "public IPv4 no match"},
		{"2001:db8::1", true, "IPv6 CIDR match"},
		{"2001:db9::1", false, "IPv6 outside CIDR"},
	}
	
	for _, tc := range testCases {
		result := matcher.IsIPMatched(tc.ip)
		if result != tc.expected {
			t.Errorf("Test case '%s': expected %v, got %v for IP %s", 
				tc.desc, tc.expected, result, tc.ip)
		}
	}
}

func TestIPMatcher_Performance(t *testing.T) {
	matcher := NewIPMatcher()
	
	// 创建大量IP规则
	ipList := "***********/16, 10.0.0.0/8, **********/12, ***********/24"
	for i := 1; i <= 100; i++ {
		ipList += ", 192.168." + string(rune(i)) + ".0/24"
	}
	
	err := matcher.BuildFromIPList(ipList)
	if err != nil {
		t.Fatalf("Failed to build IP matcher: %v", err)
	}
	
	// 性能测试
	testIP := "**************"
	iterations := 10000
	
	start := time.Now()
	for i := 0; i < iterations; i++ {
		matcher.IsIPMatched(testIP)
	}
	duration := time.Since(start)
	
	t.Logf("Performance test: %d iterations took %v (avg: %v per match)", 
		iterations, duration, duration/time.Duration(iterations))
	
	// 获取统计信息
	stats := matcher.GetStats()
	t.Logf("Matcher stats: %+v", stats)
}

func TestModel_IsIPInBlacklistOptimized(t *testing.T) {
	model := &Model{
		ChannelID: 1,
		IPList:    "***********, 10.0.0.0/8, **********/12",
		Status:    1, // 启用状态
	}
	
	// 测试用例
	testCases := []struct {
		ip       string
		expected bool
		desc     string
	}{
		{"***********", true, "exact match"},
		{"********", true, "CIDR match"},
		{"************", true, "CIDR match"},
		{"*******", false, "no match"},
	}
	
	for _, tc := range testCases {
		result := model.IsIPInBlacklist(tc.ip)
		if result != tc.expected {
			t.Errorf("Test case '%s': expected %v, got %v for IP %s", 
				tc.desc, tc.expected, result, tc.ip)
		}
	}
	
	// 测试统计信息
	stats := model.GetIPMatcherStats()
	t.Logf("Model IP matcher stats: %+v", stats)
}

func TestModel_RefreshIPMatcher(t *testing.T) {
	model := &Model{
		ChannelID: 1,
		IPList:    "***********",
		Status:    1,
	}
	
	// 初始测试
	if !model.IsIPInBlacklist("***********") {
		t.Error("Initial IP should match")
	}
	
	// 更新IP列表
	model.IPList = "********, **********/16"
	err := model.RefreshIPMatcher()
	if err != nil {
		t.Fatalf("Failed to refresh IP matcher: %v", err)
	}
	
	// 测试更新后的匹配
	if model.IsIPInBlacklist("***********") {
		t.Error("Old IP should not match after refresh")
	}
	
	if !model.IsIPInBlacklist("********") {
		t.Error("New IP should match after refresh")
	}
	
	if !model.IsIPInBlacklist("************") {
		t.Error("New CIDR should match after refresh")
	}
}

func BenchmarkIPMatcher_IsIPMatched(b *testing.B) {
	matcher := NewIPMatcher()
	ipList := "***********/16, 10.0.0.0/8, **********/12"
	matcher.BuildFromIPList(ipList)
	
	testIP := "*************"
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		matcher.IsIPMatched(testIP)
	}
}

func BenchmarkModel_IsIPInBlacklistLegacy(b *testing.B) {
	model := &Model{
		ChannelID: 1,
		IPList:    "***********/16, 10.0.0.0/8, **********/12",
		Status:    1,
	}
	
	testIP := "*************"
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		model.isIPInBlacklistLegacy(testIP)
	}
}

func BenchmarkModel_IsIPInBlacklistOptimized(b *testing.B) {
	model := &Model{
		ChannelID: 1,
		IPList:    "***********/16, 10.0.0.0/8, **********/12",
		Status:    1,
	}
	
	testIP := "*************"
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		model.IsIPInBlacklist(testIP)
	}
}
