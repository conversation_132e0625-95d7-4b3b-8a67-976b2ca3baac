package resource_ip_disallow

import (
	"net"
	"strings"

	"vlab/app/common/dbs"

	"github.com/samber/lo"
)

type Model struct {
	dbs.ModelWithDel
	ChannelID uint64 `json:"channel_id,omitempty"` // 渠道ID
	IPList    string `json:"ip_list,omitempty"`    // IP地址列表，CIDR格式，逗号分隔
	Status    uint32 `json:"status,omitempty"`     // 状态：1-启用，0-禁用

	// 内存中的IP匹配器，不序列化到JSON
	ipMatcher *IPMatcher `json:"-"`
}

func (m *Model) TableName() string {
	return "resource_ip_disallow"
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

// GetIPCIDRList 获取IP CIDR列表
func (m *Model) GetIPCIDRList() []string {
	if m.IPList == "" {
		return []string{}
	}
	ipList := strings.Split(m.IPList, ",")
	return lo.Map(ipList, func(ip string, _ int) string {
		return strings.TrimSpace(ip)
	})
}

// IsIPInBlacklist 检查IP是否在黑名单中（优化版本，使用Trie树）
func (m *Model) IsIPInBlacklist(clientIP string) bool {
	if m.Status != uint32(dbs.StatusEnable) {
		return false
	}

	ipList := m.GetIPCIDRList()
	if len(ipList) == 0 {
		return false
	}

	// 初始化或更新IP匹配器
	if m.ipMatcher == nil {
		m.ipMatcher = NewIPMatcher()
		if err := m.ipMatcher.BuildFromIPList(m.IPList); err != nil {
			// 如果构建失败，fallback到传统方法
			return m.isIPInBlacklistLegacy(clientIP)
		}
	}

	// 使用优化的匹配器
	return m.ipMatcher.IsIPMatched(clientIP)
}

// isIPInBlacklistLegacy 传统的IP匹配方法（作为备用）
func (m *Model) isIPInBlacklistLegacy(clientIP string) bool {
	ipList := m.GetIPCIDRList()
	if len(ipList) == 0 {
		return false
	}

	// 解析客户端IP
	clientIPAddr := net.ParseIP(clientIP)
	if clientIPAddr == nil {
		return false
	}

	// 检查每个CIDR
	for _, cidr := range ipList {
		if cidr == "" {
			continue
		}

		// 如果不包含/，则认为是单个IP地址
		if !strings.Contains(cidr, "/") {
			if cidr == clientIP {
				return true
			}
			continue
		}

		// 解析CIDR
		_, ipNet, err := net.ParseCIDR(cidr)
		if err != nil {
			// 如果解析失败，尝试作为单个IP处理
			if cidr == clientIP {
				return true
			}
			continue
		}

		// 检查IP是否在CIDR范围内
		if ipNet.Contains(clientIPAddr) {
			return true
		}
	}

	return false
}

// RefreshIPMatcher 刷新IP匹配器（当IP列表更新时调用）
func (m *Model) RefreshIPMatcher() error {
	if m.ipMatcher == nil {
		m.ipMatcher = NewIPMatcher()
	}
	return m.ipMatcher.BuildFromIPList(m.IPList)
}

// GetIPMatcherStats 获取IP匹配器统计信息
func (m *Model) GetIPMatcherStats() map[string]interface{} {
	if m.ipMatcher == nil {
		return map[string]interface{}{
			"status": "not_initialized",
		}
	}
	return m.ipMatcher.GetStats()
}

type Filter struct {
	ID        uint64
	ChannelID uint64
	Status    uint32
	Sort      dbs.CommonSort
}

type ModelList []*Model

func (ml ModelList) GetIDs() []uint64 {
	return lo.Map(ml, func(item *Model, _ int) uint64 {
		return item.ID
	})
}

func (ml ModelList) GetChannelIDs() []uint64 {
	return lo.Uniq(lo.Map(ml, func(item *Model, _ int) uint64 {
		return item.ChannelID
	}))
}

func (ml ModelList) GetChannelMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		retMap[val.ChannelID] = val
	}
	return retMap
}

func (ml ModelList) GetIDMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		retMap[val.ID] = val
	}
	return retMap
}
