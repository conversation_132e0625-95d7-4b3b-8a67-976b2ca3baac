package resource_ip_disallow

import (
	"fmt"
	"vlab/app/common/dbs"
	redisPkg "vlab/pkg/redis"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
)

// RedisIPDisallowList 获取IP黑名单列表
func (e *Entry) RedisIPDisallowList(ctx *gin.Context) (ModelList, error) {
	cacheKey := redisPkg.IPDisallowListKey
	ret := ModelList{}
	cacheData, err := e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
	if err != nil {
		if err.Error() == redis.Nil.Error() {
			return e.RedisReloadIPDisallowList(ctx)
		}
		return nil, err
	}
	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		return nil, err
	}
	return ret, nil
}

// RedisIPDisallowMap 获取IP黑名单ID映射
func (e *Entry) RedisIPDisallowMap(ctx *gin.Context) (map[uint64]*Model, error) {
	list, err := e.RedisIPDisallowList(ctx)
	if err != nil {
		return nil, err
	}
	return list.GetIDMap(), nil
}

// RedisIPDisallowChannelMap 获取IP黑名单渠道映射
func (e *Entry) RedisIPDisallowChannelMap(ctx *gin.Context) (map[uint64]*Model, error) {
	list, err := e.RedisIPDisallowList(ctx)
	if err != nil {
		return nil, err
	}
	return list.GetChannelMap(), nil
}

// RedisReloadIPDisallowList 重新加载IP黑名单列表到缓存
func (e *Entry) RedisReloadIPDisallowList(ctx *gin.Context) (ModelList, error) {
	list, err := e.FindByFilter(ctx, &Filter{
		Status: uint32(dbs.StatusEnable), // 只缓存启用的记录
	})
	if err != nil {
		return nil, err
	}

	cacheKey := redisPkg.IPDisallowListKey
	cacheData, err := json.Marshal(list)
	if err != nil {
		return nil, err
	}

	// 缓存5分钟
	if err = e.RedisCli.Set(ctx.Request.Context(), cacheKey, cacheData, redisPkg.LockTimeFiveMinute).Err(); err != nil {
		return nil, err
	}
	return list, nil
}

// RedisClearIPDisallowList 清除IP黑名单缓存
func (e *Entry) RedisClearIPDisallowList(ctx *gin.Context) error {
	cacheKey := redisPkg.IPDisallowListKey
	return e.RedisCli.Del(ctx.Request.Context(), cacheKey).Err()
}

// RedisIPDisallowByChannelID 按渠道ID获取IP黑名单配置（分层缓存优化版本）
func (e *Entry) RedisIPDisallowByChannelID(ctx *gin.Context, channelID uint64) (*Model, error) {
	cacheKey := fmt.Sprintf("vlab:cache:ipDisallow:channel:%d", channelID)
	ret := &Model{}

	cacheData, err := e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
	if err != nil {
		if err.Error() == redis.Nil.Error() {
			// 缓存未命中，从数据库加载并缓存
			return e.RedisReloadIPDisallowByChannelID(ctx, channelID)
		}
		return nil, err
	}

	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		return nil, err
	}
	return ret, nil
}

// RedisReloadIPDisallowByChannelID 重新加载指定渠道的IP黑名单配置到缓存
func (e *Entry) RedisReloadIPDisallowByChannelID(ctx *gin.Context, channelID uint64) (*Model, error) {
	// 从数据库查询指定渠道的配置
	config, err := e.FetchByChannelID(ctx, channelID)
	if err != nil {
		return nil, err
	}

	// 如果没有找到配置或配置未启用，缓存空配置避免缓存穿透
	if config == nil || config.ID == 0 || config.Status != uint32(dbs.StatusEnable) {
		config = &Model{} // 空配置
	}

	cacheKey := fmt.Sprintf("vlab:cache:ipDisallow:channel:%d", channelID)
	cacheData, err := json.Marshal(config)
	if err != nil {
		return nil, err
	}

	// 缓存5分钟
	if err = e.RedisCli.Set(ctx.Request.Context(), cacheKey, cacheData, redisPkg.LockTimeFiveMinute).Err(); err != nil {
		return nil, err
	}

	return config, nil
}

// RedisClearIPDisallowByChannelID 清除指定渠道的IP黑名单缓存
func (e *Entry) RedisClearIPDisallowByChannelID(ctx *gin.Context, channelID uint64) error {
	cacheKey := fmt.Sprintf("vlab:cache:ipDisallow:channel:%d", channelID)
	return e.RedisCli.Del(ctx.Request.Context(), cacheKey).Err()
}
