package middleware

import (
	"vlab/app/dao/resource_ip_disallow"
	"vlab/pkg/helper"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

const (
	// ForceAuditStatusKey 强制审核状态的上下文键名
	ForceAuditStatusKey = "force_audit_status"
)

// IPBlacklistCheck IP黑名单检查中间件
// 检查当前请求的IP是否在指定渠道的黑名单中
// 如果在黑名单中，设置强制审核状态标志位
func IPBlacklistCheck() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// 获取客户端IP地址
		clientIP := ctx.ClientIP()
		if clientIP == "" {
			log.Ctx(ctx).Warn("Failed to get client IP")
			ctx.Next()
			return
		}

		// 获取渠道ID
		channelID, err := helper.GetCtxChannelID(ctx)
		if err != nil {
			// 如果无法获取渠道ID，记录日志但继续执行
			log.Ctx(ctx).WithError(err).Warn("Failed to get channel ID for IP blacklist check")
			ctx.Next()
			return
		}

		// 检查IP是否在黑名单中
		isBlacklisted, err := checkIPInBlacklist(ctx, channelID, clientIP)
		if err != nil {
			// 如果检查过程中出错，记录日志但不阻断请求
			log.Ctx(ctx).WithError(err).WithFields(logrus.Fields{
				"channel_id": channelID,
				"client_ip":  clientIP,
			}).Error("Failed to check IP blacklist")
			ctx.Next()
			return
		}

		// 如果IP在黑名单中，设置强制审核状态
		if isBlacklisted {
			ctx.Set(ForceAuditStatusKey, true)
			log.Ctx(ctx).WithFields(logrus.Fields{
				"channel_id": channelID,
				"client_ip":  clientIP,
			}).Info("IP is in blacklist, setting force audit status")
		} else {
			ctx.Set(ForceAuditStatusKey, false)
		}

		ctx.Next()
	}
}

// checkIPInBlacklist 检查IP是否在指定渠道的黑名单中（优化版本，使用分层缓存）
func checkIPInBlacklist(ctx *gin.Context, channelID uint64, clientIP string) (bool, error) {
	cacheManager := resource_ip_disallow.GetCacheManager()

	// 优先使用分层缓存
	ipDisallowConfig, err := resource_ip_disallow.GetRepo().RedisIPDisallowByChannelID(ctx, channelID)
	if err != nil {
		// 记录缓存错误
		cacheManager.RecordCacheError()
		// 如果分层缓存失败，尝试全局缓存
		log.Ctx(ctx).WithError(err).Warn("Failed to get IP blacklist from layered cache, trying global cache")
		return checkIPInBlacklistFromGlobalCache(ctx, channelID, clientIP)
	}

	// 检查配置是否有效
	if ipDisallowConfig == nil || ipDisallowConfig.ID == 0 {
		// 该渠道没有IP黑名单配置，记录缓存命中（空配置也是有效缓存）
		cacheManager.RecordCacheHit()
		return false, nil
	}

	// 记录缓存命中
	cacheManager.RecordCacheHit()

	// 检查IP是否在黑名单中
	return ipDisallowConfig.IsIPInBlacklist(clientIP), nil
}

// checkIPInBlacklistFromGlobalCache 从全局缓存检查IP黑名单（备用方案）
func checkIPInBlacklistFromGlobalCache(ctx *gin.Context, channelID uint64, clientIP string) (bool, error) {
	cacheManager := resource_ip_disallow.GetCacheManager()

	// 首先尝试从缓存中获取渠道IP黑名单映射
	channelMap, err := resource_ip_disallow.GetRepo().RedisIPDisallowChannelMap(ctx)
	if err != nil {
		// 记录缓存错误
		cacheManager.RecordCacheError()
		// 如果缓存失败，直接从数据库查询
		log.Ctx(ctx).WithError(err).Warn("Failed to get IP blacklist from global cache, querying database")
		return checkIPInBlacklistFromDB(ctx, channelID, clientIP)
	}

	// 检查该渠道是否有IP黑名单配置
	ipDisallowConfig, exists := channelMap[channelID]
	if !exists || ipDisallowConfig == nil {
		// 该渠道没有IP黑名单配置，记录缓存命中
		cacheManager.RecordCacheHit()
		return false, nil
	}

	// 记录缓存命中
	cacheManager.RecordCacheHit()

	// 检查IP是否在黑名单中
	return ipDisallowConfig.IsIPInBlacklist(clientIP), nil
}

// checkIPInBlacklistFromDB 直接从数据库检查IP是否在黑名单中
func checkIPInBlacklistFromDB(ctx *gin.Context, channelID uint64, clientIP string) (bool, error) {
	cacheManager := resource_ip_disallow.GetCacheManager()

	// 记录缓存未命中
	cacheManager.RecordCacheMiss()

	ipDisallowConfig, err := resource_ip_disallow.GetRepo().FetchByChannelID(ctx, channelID)
	if err != nil {
		return false, err
	}

	if ipDisallowConfig == nil || ipDisallowConfig.ID == 0 {
		// 该渠道没有IP黑名单配置
		return false, nil
	}

	// 检查IP是否在黑名单中
	return ipDisallowConfig.IsIPInBlacklist(clientIP), nil
}

// GetForceAuditStatus 获取强制审核状态
// 返回值：true表示需要强制审核，false表示不需要
func GetForceAuditStatus(ctx *gin.Context) bool {
	if val, ok := ctx.Get(ForceAuditStatusKey); ok && val != nil {
		if forceAudit, OK := val.(bool); OK {
			return forceAudit
		}
	}
	return false
}

// SetForceAuditStatus 设置强制审核状态（用于测试或特殊情况）
func SetForceAuditStatus(ctx *gin.Context, forceAudit bool) {
	ctx.Set(ForceAuditStatusKey, forceAudit)
}
