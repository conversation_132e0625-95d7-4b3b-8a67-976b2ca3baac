package redis

import "fmt"

const (
	MenuListKey     = "vlab:cache:list:menu"
	RoleListKey     = "vlab:cache:list:role"
	AccountListKey  = "vlab:cache:list:account"
	AuthMenuListKey = "vlab:cache:list:authMenu"
	ConfigListKey   = "vlab:cache:list:config"

	AccountTokenKey           = "vlab:cache:token:account:%d"
	UserTokenKey              = "vlab:cache:token:user:%d:ctype:%d"
	UserInfoKey               = "vlab:cache:info:user:%d"
	DeviceInfoKey             = "vlab:cache:info:device:%v"
	ClientReqSlidingWindowKey = "vlab:cache:slidingWin:clientReq:%s"
	DeviceNoSlidingWindowKey  = "vlab:cache:slidingWin:deviceNo:%s"
	WatchAdKey                = "vlab:cache:watchAd:date:%v:etype_eid:%d_%d"
	WatchVideoSetKey          = "vlab:cache:watchVideoSet:date:%v:showID_episodeID:%d_%d"

	ChannelListKey    = "vlab:cache:list:channel"
	ChannelKeyListKey = "vlab:cache:list:channelKey"
	VersionListKey    = "vlab:cache:list:version"
	LogBlackListKey   = "vlab:cache:list:logBlack"
	VipProductListKey = "vlab:cache:list:vipProduct"
	IPDisallowListKey = "vlab:cache:list:ipDisallow"

	// 以下暂未使用
	CompanyListKey        = "vlab:cache:list:company"
	UserTypeListKey       = "vlab:cache:list:userType"
	AreaListKey           = "vlab:cache:list:area"
	BrandListKey          = "vlab:cache:list:brand"
	SupplierListKey       = "vlab:cache:list:supplier"
	StoreListKey          = "vlab:cache:list:store"
	FinanceAccountListKey = "vlab:cache:list:financeAccount"
	VendorListKey         = "vlab:cache:list:vendor"

	RoleMenuIdsKey            = "vlab:cache:roleMenuIds:%d"
	AccountInfoKey            = "vlab:cache:accountInfo:%d"
	AccountAuthMenuKey        = "vlab:cache:accountAuthMenu:%d"
	AccountAssitClientListKey = "vlab:cache:accountAssitClientList:%d"
	UserBalanceListKey        = "vlab:cache:userBalanceList:%d"
	UserCollectSpuKey         = "vlab:cache:userCollectSpu:%d"

	TagParamsKey = "vlab:cache:tagParams:%d"
	TagKey       = "vlab:cache:tag:%s"

	FieldKey           = "vlab:cache:field:%d"
	AttrKey            = "vlab:cache:attr:%d"
	AttrStrKey         = "vlab:cache:attr:%s"
	FieldAttrsKey      = "vlab:cache:field:%d:attrs"
	UserBatchCreateKey = "vlab:cache:user:batch:create:%s"

	EmailVerifyCodeKey = "vlab:cache:email:verify_code:%s"
	EmailSendTimeKey   = "vlab:cache:email:send_time:%s"

	ClientConfigListKey        = "vlab:cache:list:clientConfig"
	ClientConfigKey            = "vlab:cache:clientConfig:%s"
	ClientConfigChannelListKey = "vlab:cache:list:clientConfig:channel:%d"
	ClientConfigChannelKey     = "vlab:cache:clientConfig:channel:%d:%s"
)

func GetAccountTokenKey(aid uint64) string {
	return fmt.Sprintf(AccountTokenKey, aid)
}

func GetUserTokenKey(uid uint64, clientType int) string {
	return fmt.Sprintf(UserTokenKey, uid, clientType)
}

func GetUserInfoKey(uid uint64) string {
	return fmt.Sprintf(UserInfoKey, uid)
}

func GetDeviceInfoKey(deviceID string) string {
	return fmt.Sprintf(DeviceInfoKey, deviceID)
}

func GetClientReqSlidingWindowKey(intvalKey string) string {
	return fmt.Sprintf(ClientReqSlidingWindowKey, intvalKey)
}

func GetDeviceNoSlidingWindowKey(intvalKey string) string {
	return fmt.Sprintf(DeviceNoSlidingWindowKey, intvalKey)
}

func GetWatchAdKey(date string, entityID uint64, entityType int) string {
	return fmt.Sprintf(WatchAdKey, date, entityType, entityID)
}

func GetWatchVideoSetKey(date string, showID, episodeID uint64) string {
	return fmt.Sprintf(WatchVideoSetKey, date, showID, episodeID)
}

// 以下暂未使用
func GetFieldKey(fid uint64) string {
	return fmt.Sprintf(FieldKey, fid)
}

func GetAttrKey(aid uint64) string {
	return fmt.Sprintf(AttrKey, aid)
}

func GetAttrStrKey(aid string) string {
	return fmt.Sprintf(AttrStrKey, aid)
}

func GetFieldAttrsKey(fid uint64) string {
	return fmt.Sprintf(FieldAttrsKey, fid)
}

func GetRoleMenuIdsKey(rid uint64) string {
	return fmt.Sprintf(RoleMenuIdsKey, rid)
}

func GetAccountInfoKey(aid uint64) string {
	return fmt.Sprintf(AccountInfoKey, aid)
}

func GetAccountAuthMenuKey(aid uint64) string {
	return fmt.Sprintf(AccountAuthMenuKey, aid)
}

func GetAccountAssitClientListKey(aid uint64) string {
	return fmt.Sprintf(AccountAssitClientListKey, aid)
}

func GetUserBalanceListKey(uid uint64) string {
	return fmt.Sprintf(UserBalanceListKey, uid)
}

func GetTagParamsKey(groupId uint64) string {
	return fmt.Sprintf(TagParamsKey, groupId)
}

func GetTagKey(name string) string {
	return fmt.Sprintf(TagKey, name)
}

func GetUserCollectSpuKey(uid uint64) string {
	return fmt.Sprintf(UserCollectSpuKey, uid)
}

func GetUserBatchCreateKey(randNum string) string {
	return fmt.Sprintf(UserBatchCreateKey, randNum)
}

// GetVerificationCodeKey 获取验证码Redis键
func GetVerificationCodeKey(email string) string {
	return fmt.Sprintf(EmailVerifyCodeKey, email)
}

// GetSendTimeKey 获取发送时间Redis键
func GetSendTimeKey(email string) string {
	return fmt.Sprintf(EmailSendTimeKey, email)
}

// GetClientConfigKey 获取单个页面配置缓存键
func GetClientConfigKey(key string) string {
	return fmt.Sprintf(ClientConfigKey, key)
}

// GetClientConfigChannelListKey 获取渠道页面配置列表缓存键
func GetClientConfigChannelListKey(channelID uint64) string {
	return fmt.Sprintf(ClientConfigChannelListKey, channelID)
}

// GetClientConfigChannelKey 获取单个渠道页面配置缓存键
func GetClientConfigChannelKey(channelID uint64, key string) string {
	return fmt.Sprintf(ClientConfigChannelKey, channelID, key)
}
